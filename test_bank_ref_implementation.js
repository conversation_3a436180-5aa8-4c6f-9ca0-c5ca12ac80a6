// Test script to verify bank_ref field implementation
const { Pool } = require('pg');

const pool = new Pool({
  host: 'ep-dawn-fog-a1jk7z7f-pooler.ap-southeast-1.aws.neon.tech',
  database: 'neondb',
  user: 'neondb_owner',
  password: 'npg_v1aKnJdNXif4',
  port: 5432,
  ssl: { rejectUnauthorized: false }
});

async function testBankRefImplementation() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Testing bank_ref field implementation...\n');

    // 1. Test SELECT with bank_ref
    console.log('1. Testing SELECT query with bank_ref:');
    const selectQuery = `
      SELECT bank_id, bank_code, bank_name_th, bank_name_en,
             bank_address_th, bank_address_en, bank_ref, active,
             create_by, create_dt, update_by, update_dt
      FROM tmst_bank
      LIMIT 3
    `;
    
    const selectResult = await client.query(selectQuery);
    console.log(`✅ SELECT query successful. Found ${selectResult.rows.length} banks:`);
    selectResult.rows.forEach(bank => {
      console.log(`  - ${bank.bank_code}: ${bank.bank_name_en} | Ref: ${bank.bank_ref || 'NULL'}`);
    });
    console.log('');

    // 2. Test INSERT with bank_ref
    console.log('2. Testing INSERT with bank_ref:');
    const timestamp = Date.now().toString().slice(-8); // Use last 8 digits
    const testBankCode = `TST${timestamp}`;
    const testBankRef = `REF${timestamp}`;
    
    const insertQuery = `
      INSERT INTO tmst_bank (bank_code, bank_name_th, bank_name_en, bank_address_th, bank_address_en, bank_ref, active, create_by)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING bank_id, bank_code, bank_name_th, bank_name_en,
                bank_address_th, bank_address_en, bank_ref, active,
                create_by, create_dt, update_by, update_dt
    `;
    
    const insertValues = [
      testBankCode,
      'Test Bank Thai',
      'Test Bank English',
      'Test Address Thai',
      'Test Address English',
      testBankRef,
      true,
      'TEST_SYSTEM'
    ];
    
    const insertResult = await client.query(insertQuery, insertValues);
    const newBank = insertResult.rows[0];
    console.log(`✅ INSERT successful. Created bank ID: ${newBank.bank_id}`);
    console.log(`   Bank Code: ${newBank.bank_code}`);
    console.log(`   Bank Ref: ${newBank.bank_ref}`);
    console.log('');

    // 3. Test UPDATE with bank_ref
    console.log('3. Testing UPDATE with bank_ref:');
    const updatedBankRef = `UPD${timestamp}`;
    
    const updateQuery = `
      UPDATE tmst_bank
      SET bank_code = $1, bank_name_th = $2, bank_name_en = $3,
          bank_address_th = $4, bank_address_en = $5, bank_ref = $6, active = $7,
          update_by = $8, update_dt = CURRENT_TIMESTAMP
      WHERE bank_id = $9
      RETURNING bank_id, bank_code, bank_name_th, bank_name_en,
                bank_address_th, bank_address_en, bank_ref, active,
                create_by, create_dt, update_by, update_dt
    `;
    
    const updateValues = [
      testBankCode,
      'Updated Test Bank Thai',
      'Updated Test Bank English',
      'Updated Test Address Thai',
      'Updated Test Address English',
      updatedBankRef,
      true,
      'TEST_UPDATE_SYSTEM',
      newBank.bank_id
    ];
    
    const updateResult = await client.query(updateQuery, updateValues);
    const updatedBank = updateResult.rows[0];
    console.log(`✅ UPDATE successful. Updated bank ID: ${updatedBank.bank_id}`);
    console.log(`   Bank Code: ${updatedBank.bank_code}`);
    console.log(`   Bank Ref: ${updatedBank.bank_ref}`);
    console.log('');

    // 4. Test search functionality with bank_ref
    console.log('4. Testing search functionality with bank_ref:');
    const searchQuery = `
      SELECT bank_id, bank_code, bank_name_th, bank_name_en, bank_ref
      FROM tmst_bank
      WHERE (
        UPPER(bank_code) LIKE UPPER($1) OR
        UPPER(bank_name_th) LIKE UPPER($2) OR
        UPPER(bank_name_en) LIKE UPPER($3) OR
        UPPER(bank_ref) LIKE UPPER($4)
      )
      AND bank_id = $5
    `;
    
    const searchTerm = `%${updatedBankRef}%`;
    const searchValues = [searchTerm, searchTerm, searchTerm, searchTerm, newBank.bank_id];
    
    const searchResult = await client.query(searchQuery, searchValues);
    console.log(`✅ SEARCH successful. Found ${searchResult.rows.length} banks matching ref "${updatedBankRef}"`);
    searchResult.rows.forEach(bank => {
      console.log(`   - ${bank.bank_code}: ${bank.bank_name_en} | Ref: ${bank.bank_ref}`);
    });
    console.log('');

    // 5. Clean up test data
    console.log('5. Cleaning up test data:');
    const deleteQuery = `DELETE FROM tmst_bank WHERE bank_id = $1`;
    await client.query(deleteQuery, [newBank.bank_id]);
    console.log(`✅ Test bank deleted successfully (ID: ${newBank.bank_id})`);

  } finally {
    client.release();
  }
}

testBankRefImplementation()
  .then(() => {
    console.log('\n✅ All bank_ref implementation tests completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Bank_ref implementation tests failed:', error);
    process.exit(1);
  });
